// Utility functions for the CAF appointment system

/**
 * Check if a date is a weekday (Monday-Friday)
 * @param {string} dateString - Date string in YYYY-MM-DD format
 * @returns {boolean} - True if weekday, false if weekend
 */
export const isWeekday = (dateString) => {
  const date = new Date(dateString);
  const day = date.getDay();
  return day >= 1 && day <= 5; // Monday = 1, Friday = 5
};

/**
 * Get minimum date (today) in YYYY-MM-DD format
 * @returns {string} - Today's date in YYYY-MM-DD format
 */
export const getMinDate = () => {
  const today = new Date();
  return today.toISOString().split('T')[0];
};

/**
 * Format date for display in Italian locale
 * @param {string} dateString - Date string in YYYY-MM-DD format
 * @returns {string} - Formatted date string
 */
export const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('it-IT', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

/**
 * Validate phone number format
 * @param {string} phone - Phone number string
 * @returns {boolean} - True if valid format
 */
export const isValidPhone = (phone) => {
  const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
  return phoneRegex.test(phone);
};

/**
 * Validate email format
 * @param {string} email - Email string
 * @returns {boolean} - True if valid format
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
  return emailRegex.test(email);
};

/**
 * Available time slots for public appointments (every 30 minutes) - Limited to 18:30 for public bookings
 */
export const publicTimeSlots = [
  '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00',
  '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00',
  '18:30', '19:00', '19:30', '20:00'
];

/**
 * Available time slots for admin appointments (every 30 minutes) - Extended to 20:00 for admin bookings
 */
export const adminTimeSlots = [
  '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00',
  '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00',
  '18:30', '19:00', '19:30', '20:00'
];

/**
 * Default time slots (for backward compatibility) - Uses admin slots for full range
 */
export const timeSlots = adminTimeSlots;

/**
 * Available services with their corresponding prestazioni
 */
export const services = [
  'Servizi CAF',
  'Patronato',
  'Avvocato',
  'Medico',
  'Prestiti',
  'Immigrazione',
  'Altri Servizi'
];

/**
 * Service to prestazioni mapping
 */
export const servicePrestazioni = {
  'Servizi CAF': [
    'ISEE',
    'IMU',
    'Modello 730',
    'Modello Unico PF',
    'Dimissioni Lavoro Volontarie',
    'Test Italiano'
  ],
  'Patronato': [
    'Pensione ai Superstiti',
    'Indennità di Accompagnamento',
    'Controllo Contributi',
    'Domanda Pensione Sociale',
    'Rate Tredicesima',
    'Pensione di Invalidità',
    'Pensione Indiretta',
    'Previsione Pensione',
    'Domanda Pensione',
    'Naspi'
  ],
  'Avvocato': [
    'Avvocato per Invalidità',
    'Avvocato per Lavoro',
    'Avvocato Problemi Condominiali',
    'Avvocato Contratti/Cartelle Esattoriali',
    'Avvocato Penalista'
  ],
  'Medico': [
    'Medico Certificatore per Invalidità'
  ],
  'Prestiti': [
    'AGOS da 3.000€ a 50.000€'
  ],
  'Immigrazione': [
    'Rinnovo/Aggiornamento Permesso di Soggiorno',
    'Cittadinanza',
    'Idoneità alloggiativa',
    'Ricongiungimento Familiare'
  ],
  'Altri Servizi': [
    'Rinnovo Patente',
    'SPID',
    'Pagamento Utenze',
    'Pagamento F24',
    'Delega 730',
    'Tariffa ATAC',
    'Iscrizione/Cessazione TARI',
    'Ritiro Documenti',
    'Ritiro ISEE'
  ]
};

/**
 * Get time slot display information
 * @param {string} time - Time in HH:MM format
 * @returns {Object} - Time slot information
 */
export const getTimeSlotInfo = (time) => {
  const hour = parseInt(time.split(':')[0]);
  const isMorning = hour >= 9 && hour <= 13;
  const isAfternoon = hour >= 15 && hour <= 21;

  return {
    time,
    hour,
    isMorning,
    isAfternoon,
    period: isMorning ? 'morning' : 'afternoon',
    periodLabel: isMorning ? 'Mattina' : 'Pomeriggio'
  };
};

/**
 * Group time slots by period
 * @param {Array} slots - Array of time slots
 * @returns {Object} - Grouped time slots
 */
export const groupTimeSlotsByPeriod = (slots) => {
  const morning = [];
  const afternoon = [];

  slots.forEach(slot => {
    const info = getTimeSlotInfo(slot);
    if (info.isMorning) {
      morning.push(slot);
    } else if (info.isAfternoon) {
      afternoon.push(slot);
    }
  });

  return { morning, afternoon };
};

/**
 * Format time for display
 * @param {string} time - Time in HH:MM format
 * @returns {string} - Formatted time
 */
export const formatTimeForDisplay = (time) => {
  return time; // For now, just return as-is, but could be enhanced for different formats
};

/**
 * Check if a date is today
 * @param {string} dateString - Date string in YYYY-MM-DD format
 * @returns {boolean} - True if date is today
 */
export const isToday = (dateString) => {
  const today = new Date().toISOString().split('T')[0];
  return dateString === today;
};

/**
 * Check if a date is in the future
 * @param {string} dateString - Date string in YYYY-MM-DD format
 * @returns {boolean} - True if date is in the future
 */
export const isFutureDate = (dateString) => {
  const selectedDate = new Date(dateString);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return selectedDate >= today;
};
