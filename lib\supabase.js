import { createClient } from '@supabase/supabase-js';

// Client-side variables (available in browser)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Server-side variables (available only on server)
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Create Supabase client for public operations (booking, availability)
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Create Supabase client with service role for admin operations (bypasses RLS)
export const supabaseAdmin = supabaseServiceKey
  ? createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
  : supabase; // Fallback to regular client if service key not available

// Create Supabase client for client-side operations (browser)
export const createSupabaseClient = () => {
  return createClient(supabaseUrl, supabaseAnonKey);
};

// Database table names
export const TABLES = {
  APPOINTMENTS: 'appointments',
  ADMIN_USERS: 'admin_users',
  EMPLOYEES: 'employees',
  EMPLOYEE_AVAILABILITY: 'employee_availability',
  EMPLOYEE_SPECIAL_SCHEDULES: 'employee_special_schedules'
};

// Appointment status constants
export const APPOINTMENT_STATUS = {
  CONFIRMED: 'confirmed',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  NO_SHOW: 'no_show'
};

// Service types (updated with new service structure)
export const SERVICE_TYPES = [
  'Servizi CAF',
  'Patronato',
  'Avvocato',
  'Medico',
  'Prestiti',
  'Immigrazione',
  'Altri Servizi'
];

// Time slots (based on current system)
export const TIME_SLOTS = [
  '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00',
  '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00'
];

/**
 * Helper function to handle Supabase errors
 * @param {Object} error - Supabase error object
 * @param {string} operation - Description of the operation that failed
 * @throws {Error} Formatted error message
 */
export const handleSupabaseError = (error, operation) => {
  console.error(`Supabase error during ${operation}:`, error);
  
  if (error.code === 'PGRST116') {
    throw new Error('Record not found');
  }
  
  if (error.code === '23505') {
    throw new Error('Duplicate entry - this appointment slot may already be booked');
  }
  
  if (error.code === '23514') {
    throw new Error('Invalid data provided');
  }
  
  throw new Error(error.message || `Failed to ${operation}`);
};

/**
 * Convert appointment data from JSON format to Supabase format
 * @param {Object} jsonAppointment - Appointment in JSON format
 * @returns {Object} Appointment in Supabase format
 */
export const convertJsonToSupabaseFormat = (jsonAppointment) => {
  const supabaseData = {
    id: parseInt(jsonAppointment.id),
    nome: jsonAppointment.nome,
    cognome: jsonAppointment.cognome,
    telefono: jsonAppointment.telefono,
    email: jsonAppointment.email,
    servizio: jsonAppointment.servizio,
    data_appuntamento: jsonAppointment.dataAppuntamento,
    status: jsonAppointment.status || APPOINTMENT_STATUS.CONFIRMED,
    created_at: jsonAppointment.createdAt,
    updated_at: jsonAppointment.updatedAt || jsonAppointment.createdAt,
    custom_time_range: jsonAppointment.customTimeRange || false
  };

  // Add time fields based on appointment type
  if (jsonAppointment.customTimeRange) {
    supabaseData.start_time = jsonAppointment.startTime;
    supabaseData.end_time = jsonAppointment.endTime;
    // For custom time ranges, use start_time as orario to maintain TIME type compatibility
    supabaseData.orario = jsonAppointment.startTime;
  } else {
    supabaseData.orario = jsonAppointment.orario;
    supabaseData.start_time = null;
    supabaseData.end_time = null;
  }

  return supabaseData;
};

/**
 * Convert appointment data from Supabase format to JSON format (for API compatibility)
 * @param {Object} supabaseAppointment - Appointment in Supabase format
 * @returns {Object} Appointment in JSON format
 */
export const convertSupabaseToJsonFormat = (supabaseAppointment) => {
  const isCustomTimeRange = supabaseAppointment.custom_time_range || false;

  return {
    id: supabaseAppointment.id.toString(),
    nome: supabaseAppointment.nome,
    cognome: supabaseAppointment.cognome,
    telefono: supabaseAppointment.telefono,
    email: supabaseAppointment.email,
    servizio: supabaseAppointment.servizio,
    prestazione: supabaseAppointment.prestazione,
    operatore: supabaseAppointment.operatore,
    noteAggiuntive: supabaseAppointment.note_aggiuntive,
    dataAppuntamento: supabaseAppointment.data_appuntamento,
    // For display purposes, show the formatted range for custom time ranges
    orario: isCustomTimeRange && supabaseAppointment.start_time && supabaseAppointment.end_time
      ? `${supabaseAppointment.start_time}-${supabaseAppointment.end_time}`
      : supabaseAppointment.orario,
    customTimeRange: isCustomTimeRange,
    startTime: supabaseAppointment.start_time,
    endTime: supabaseAppointment.end_time,
    status: supabaseAppointment.status,
    createdAt: supabaseAppointment.created_at,
    updatedAt: supabaseAppointment.updated_at
  };
};

/**
 * Check if a date is a weekday (Monday to Friday)
 * @param {string} dateString - Date in YYYY-MM-DD format
 * @returns {boolean} True if weekday, false if weekend
 */
export const isWeekday = (dateString) => {
  const date = new Date(dateString);
  const day = date.getDay();
  return day >= 1 && day <= 5; // Monday = 1, Friday = 5
};

/**
 * Validate appointment data
 * @param {Object} appointmentData - Appointment data to validate
 * @throws {Error} If validation fails
 */
export const validateAppointmentData = (appointmentData) => {
  const required = ['nome', 'cognome', 'servizio', 'dataAppuntamento', 'orario'];

  for (const field of required) {
    if (!appointmentData[field] || appointmentData[field].toString().trim() === '') {
      throw new Error(`Field ${field} is required`);
    }
  }

  // Validate email format only if email is provided and not "Non registrato"
  if (appointmentData.email &&
      appointmentData.email.trim() !== '' &&
      appointmentData.email !== 'Non registrato') {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(appointmentData.email)) {
      throw new Error('Invalid email format');
    }
  }

  // Note: prestazione is optional as some services might not require it
  
  // Validate date format
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(appointmentData.dataAppuntamento)) {
    throw new Error('Invalid date format. Use YYYY-MM-DD');
  }
  
  // Validate time format based on appointment type
  const timeRegex = /^\d{2}:\d{2}$/;

  if (appointmentData.customTimeRange) {
    // Validate custom time range
    if (!appointmentData.startTime || !appointmentData.endTime) {
      throw new Error('Start time and end time are required for custom time range appointments');
    }

    if (!timeRegex.test(appointmentData.startTime) || !timeRegex.test(appointmentData.endTime)) {
      throw new Error('Invalid time format for custom time range. Use HH:MM');
    }

    // Validate that end time is after start time
    const start = new Date(`2000-01-01T${appointmentData.startTime}:00`);
    const end = new Date(`2000-01-01T${appointmentData.endTime}:00`);

    if (end <= start) {
      throw new Error('End time must be after start time');
    }

    // Validate business hours (9 AM - 6 PM)
    const businessStart = new Date(`2000-01-01T09:00:00`);
    const businessEnd = new Date(`2000-01-01T18:00:00`);

    if (start < businessStart || end > businessEnd) {
      throw new Error('Appointment times must be between 09:00 and 18:00');
    }
  } else {
    // Validate predefined slot
    if (!appointmentData.orario) {
      throw new Error('Time slot is required for predefined slot appointments');
    }

    if (!timeRegex.test(appointmentData.orario)) {
      throw new Error('Invalid time format. Use HH:MM');
    }
  }
  
  // Validate that the date is a weekday
  if (!isWeekday(appointmentData.dataAppuntamento)) {
    throw new Error('Appointments are only available Monday to Friday');
  }
  
  // Validate that the date is not in the past
  const appointmentDate = new Date(appointmentData.dataAppuntamento);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  if (appointmentDate < today) {
    throw new Error('Cannot book appointments for past dates');
  }
  
  // Validate time slot based on appointment type
  if (appointmentData.customTimeRange) {
    // For custom time ranges, orario should contain the start time
    const timeRegex = /^\d{2}:\d{2}$/;
    if (!appointmentData.orario || !timeRegex.test(appointmentData.orario)) {
      throw new Error('Invalid custom time range start time format');
    }
  } else {
    // For predefined slots, validate against TIME_SLOTS
    if (!appointmentData.orario || !TIME_SLOTS.includes(appointmentData.orario)) {
      throw new Error('Invalid time slot');
    }
  }
  
  // Validate service type
  if (!SERVICE_TYPES.includes(appointmentData.servizio)) {
    throw new Error('Invalid service type');
  }
};
